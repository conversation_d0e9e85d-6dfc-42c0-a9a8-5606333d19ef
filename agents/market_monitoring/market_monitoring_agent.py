#!/usr/bin/env python3
"""
MARKET MONITORING AGENT
Wrapper for market data agent to provide backward compatibility

This module provides a unified interface for market monitoring functionality
by wrapping the existing market data agent and data structures.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import data structures
from .data_structures import (
    MarketTick, OHLCV, MarketIndicators, MarketRegime, 
    TradingSignal, MarketMonitoringConfig, SystemMetrics
)

# Import core components
from .market_data_agent import MarketDataAgent, MarketDataPoint
from .modern_market_data_agent import ModernMarketDataAgent

logger = logging.getLogger(__name__)

class MarketMonitoringAgent:
    """
    Market Monitoring Agent - Unified interface for market data monitoring
    
    This class provides a unified interface that wraps the existing market data agents
    and provides backward compatibility for the live trading orchestrator.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the market monitoring agent"""
        self.config = config
        self.name = "MarketMonitoringAgent"
        self.running = False
        self.initialized = False
        
        # Initialize the underlying market data agent
        try:
            self.market_data_agent = ModernMarketDataAgent(config)
        except Exception:
            # Fallback to basic market data agent
            self.market_data_agent = MarketDataAgent(config)
        
        # Market data storage
        self.market_data: Dict[str, MarketDataPoint] = {}
        self.ohlcv_data: Dict[str, List[OHLCV]] = {}
        self.market_indicators: Dict[str, MarketIndicators] = {}
        
        logger.info(f"[INIT] {self.name} initialized")
    
    async def initialize(self) -> bool:
        """Initialize the market monitoring agent"""
        try:
            logger.info(f"[INIT] Initializing {self.name}...")
            
            # Initialize the underlying agent
            if hasattr(self.market_data_agent, 'initialize'):
                await self.market_data_agent.initialize()
            
            self.initialized = True
            logger.info(f"[SUCCESS] {self.name} initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize {self.name}: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the market monitoring agent"""
        try:
            if not self.initialized:
                await self.initialize()
            
            logger.info(f"[START] Starting {self.name}...")
            
            # Start the underlying agent
            if hasattr(self.market_data_agent, 'start'):
                await self.market_data_agent.start()
            
            self.running = True
            logger.info(f"[SUCCESS] {self.name} started successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start {self.name}: {e}")
            return False
    
    async def stop(self) -> bool:
        """Stop the market monitoring agent"""
        try:
            logger.info(f"[STOP] Stopping {self.name}...")
            
            # Stop the underlying agent
            if hasattr(self.market_data_agent, 'stop'):
                await self.market_data_agent.stop()
            
            self.running = False
            logger.info(f"[SUCCESS] {self.name} stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to stop {self.name}: {e}")
            return False
    
    async def is_healthy(self) -> bool:
        """Check if the agent is healthy"""
        try:
            if hasattr(self.market_data_agent, 'is_healthy'):
                return await self.market_data_agent.is_healthy()
            return self.running and self.initialized
        except Exception:
            return False
    
    def get_market_data(self, symbol: str) -> Optional[MarketDataPoint]:
        """Get latest market data for a symbol"""
        try:
            if hasattr(self.market_data_agent, 'get_market_data'):
                return self.market_data_agent.get_market_data(symbol)
            return self.market_data.get(symbol)
        except Exception as e:
            logger.error(f"[ERROR] Failed to get market data for {symbol}: {e}")
            return None
    
    def get_ohlcv_data(self, symbol: str, timeframe: str = "1min") -> List[OHLCV]:
        """Get OHLCV data for a symbol"""
        try:
            if hasattr(self.market_data_agent, 'get_ohlcv_data'):
                return self.market_data_agent.get_ohlcv_data(symbol, timeframe)
            return self.ohlcv_data.get(f"{symbol}_{timeframe}", [])
        except Exception as e:
            logger.error(f"[ERROR] Failed to get OHLCV data for {symbol}: {e}")
            return []
    
    def get_market_indicators(self, symbol: str) -> Optional[MarketIndicators]:
        """Get market indicators for a symbol"""
        try:
            return self.market_indicators.get(symbol)
        except Exception as e:
            logger.error(f"[ERROR] Failed to get market indicators for {symbol}: {e}")
            return None
    
    async def subscribe_symbols(self, symbols: List[str]) -> bool:
        """Subscribe to market data for symbols"""
        try:
            if hasattr(self.market_data_agent, 'subscribe_symbols'):
                return await self.market_data_agent.subscribe_symbols(symbols)
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to subscribe to symbols: {e}")
            return False
    
    async def unsubscribe_symbols(self, symbols: List[str]) -> bool:
        """Unsubscribe from market data for symbols"""
        try:
            if hasattr(self.market_data_agent, 'unsubscribe_symbols'):
                return await self.market_data_agent.unsubscribe_symbols(symbols)
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to unsubscribe from symbols: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        return {
            'name': self.name,
            'running': self.running,
            'initialized': self.initialized,
            'symbols_count': len(self.market_data),
            'last_update': datetime.now().isoformat()
        }

# Export the main class and data structures for backward compatibility
__all__ = [
    'MarketMonitoringAgent',
    'MarketTick',
    'OHLCV', 
    'MarketIndicators',
    'MarketRegime',
    'TradingSignal',
    'MarketMonitoringConfig',
    'SystemMetrics'
]
