#!/usr/bin/env python3
"""
Modular Signal Agent for Trading System
- Generates entry and exit signals based on strategy configurations
- Can be used by backtesting, live trading, and evolution agents
- Supports intraday trading rules and time-based filtering
- Compatible with strategies.yaml configuration format
"""

import os
import logging
import yaml
import polars as pl
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, time as dt_time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SignalAgent:
    """Modular Signal Agent for generating trading signals"""
    
    def __init__(self, strategies_config_path: str = "agents/config/strategies.yaml"):
        """Initialize Signal Agent with strategy configuration"""
        self.strategies_config_path = Path(strategies_config_path)
        self.strategies = self._load_strategies()
        logger.info(f"[SIGNAL_AGENT] Loaded {len(self.strategies)} strategies")
    
    def _load_strategies(self) -> List[Dict[str, Any]]:
        """Load strategies from YAML configuration file"""
        if not self.strategies_config_path.exists():
            logger.error(f"Strategies config file not found: {self.strategies_config_path}")
            return []
        
        try:
            with open(self.strategies_config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            strategies = config.get('strategies', [])
            logger.info(f"[LOAD] Loaded {len(strategies)} strategies from config")
            return strategies
        except Exception as e:
            logger.error(f"Error loading strategies config: {e}")
            return []
    
    def get_strategies(self, ranking_threshold: int = 0) -> List[Dict[str, Any]]:
        """Get strategies filtered by ranking threshold"""
        filtered_strategies = [s for s in self.strategies if s.get('ranking', 0) >= ranking_threshold]
        logger.debug(f"[FILTER] {len(filtered_strategies)} strategies above ranking {ranking_threshold}")
        return filtered_strategies
    
    def generate_signals(self, df: pl.DataFrame, strategy: Dict[str, Any], 
                        signal_types: List[str] = None) -> Dict[str, pl.Series]:
        """
        Generate trading signals for a given strategy
        
        Args:
            df: DataFrame with market data
            strategy: Strategy configuration dictionary
            signal_types: List of signal types to generate ['entry_long', 'entry_short', 'exit_long', 'exit_short']
        
        Returns:
            Dictionary of signal series
        """
        if signal_types is None:
            signal_types = ['entry_long', 'entry_short', 'exit_long', 'exit_short']
        
        strategy_name = strategy.get('name', 'Unknown')
        logger.debug(f"[SIGNALS] Generating signals for {strategy_name}")
        
        signals = {}
        
        for signal_type in signal_types:
            try:
                signal_series = self._generate_single_signal(df, signal_type, strategy)
                signals[signal_type] = signal_series
                logger.debug(f"[{strategy_name}] {signal_type}: {signal_series.sum()} signals")
            except Exception as e:
                logger.error(f"Error generating {signal_type} for {strategy_name}: {e}")
                signals[signal_type] = pl.Series("mask", [False] * df.height)
        
        return signals
    
    def _generate_single_signal(self, df: pl.DataFrame, signal_type: str, strategy: dict) -> pl.Series:
        """Generate a single signal type with enhanced validation"""
        strategy_name = strategy.get('name', 'Unknown')
        
        # Handle new entry/exit structure
        if signal_type.startswith('entry_'):
            side = signal_type.split('_')[1]  # 'long' or 'short'
            expr_str = strategy.get('entry', {}).get(side, "").strip()
        elif signal_type.startswith('exit_'):
            side = signal_type.split('_')[1]  # 'long' or 'short'
            expr_str = strategy.get('exit', {}).get(side, "").strip()
        else:
            # Fallback for old format
            expr_str = strategy.get(signal_type, "").strip()
        
        if not expr_str:
            logger.debug(f"No {signal_type} expression for strategy {strategy_name}")
            return pl.Series("mask", [False] * df.height)
        
        # Check available columns in dataframe
        available_cols = set(df.columns)
        logger.debug(f"Available columns: {sorted(available_cols)}")
        
        # Manual replacement approach - more reliable
        replacements = {
            'close': 'pl.col("close")',
            'open': 'pl.col("open")',
            'high': 'pl.col("high")',
            'low': 'pl.col("low")',
            'volume': 'pl.col("volume")',
            'rsi_14': 'pl.col("rsi_14")',
            'rsi_5': 'pl.col("rsi_5")',
            'ema_5': 'pl.col("ema_5")',
            'ema_10': 'pl.col("ema_10")',
            'ema_13': 'pl.col("ema_13")',
            'ema_20': 'pl.col("ema_20")',
            'ema_21': 'pl.col("ema_21")',
            'ema_50': 'pl.col("ema_50")',
            'vwap': 'pl.col("vwap")',
            'supertrend': 'pl.col("supertrend")',
            'cpr_top': 'pl.col("cpr_top")',
            'cpr_bottom': 'pl.col("cpr_bottom")',
            'macd': 'pl.col("macd")',
            'macd_signal': 'pl.col("macd_signal")',
            'bb_upper': 'pl.col("bb_upper")',
            'bb_lower': 'pl.col("bb_lower")',
            'bb_middle': 'pl.col("bb_middle")',
            'atr': 'pl.col("atr")',
            'adx': 'pl.col("adx")',
            'cci': 'pl.col("cci")',
            'mfi': 'pl.col("mfi")',
            'stoch_k': 'pl.col("stoch_k")',
            'stoch_d': 'pl.col("stoch_d")',
            'pivot': 'pl.col("pivot")',
            'resistance': 'pl.col("resistance")',
            'support': 'pl.col("support")',
            'donchian_high': 'pl.col("donchian_high")',
            'donchian_low': 'pl.col("donchian_low")',
            'vcp_pattern': 'pl.col("vcp_pattern")',
            'upward_candle': 'pl.col("upward_candle")',
            'downward_candle': 'pl.col("downward_candle")'
        }
        
        # Replace boolean operators first
        polars_expr = expr_str.replace(" and ", " & ").replace(" or ", " | ").replace(" not ", " ~")

        # Replace column names with polars expressions
        for col_name, polars_col in replacements.items():
            if col_name in available_cols:
                # Use word boundaries to avoid partial matches
                import re
                pattern = r'\b' + re.escape(col_name) + r'\b'
                polars_expr = re.sub(pattern, polars_col, polars_expr)

        # Add parentheses for proper operator precedence
        polars_expr = "( " + polars_expr.replace(" & ", " ) & ( ").replace(" | ", " ) | ( ") + " )"

        logger.debug(f"Converted expression: {polars_expr}")

        try:
            # Evaluate the expression
            expr = eval(polars_expr, {"__builtins__": {}, "pl": pl})
            result = df.select(expr.alias("signal")).to_series()
            logger.debug(f"Generated {result.sum()} signals for {signal_type}")
            return result
        except Exception as e:
            logger.error(f"Error evaluating expression '{polars_expr}' for {strategy_name}: {e}")
            return pl.Series("mask", [False] * df.height)
    
    def apply_intraday_rules(self, df: pl.DataFrame, signals: Dict[str, pl.Series], 
                           strategy: Dict[str, Any]) -> Dict[str, pl.Series]:
        """Apply intraday trading rules to filter signals based on time constraints"""
        intraday_rules = strategy.get('intraday_rules', {})
        
        if not intraday_rules:
            return signals
        
        # Parse time constraints
        no_trade_after = intraday_rules.get('no_trade_after', '14:30')
        exit_all_at = intraday_rules.get('exit_all_at', '15:10')
        
        # Convert time strings to time objects
        try:
            no_trade_time = dt_time(*map(int, no_trade_after.split(':')))
            exit_time = dt_time(*map(int, exit_all_at.split(':')))
        except ValueError:
            logger.warning(f"Invalid time format in intraday rules: {no_trade_after}, {exit_all_at}")
            return signals
        
        # Extract time from datetime column
        if 'datetime' not in df.columns:
            logger.warning("No datetime column found for intraday rules")
            return signals
        
        df_with_time = df.with_columns([
            pl.col('datetime').dt.time().alias('time_only')
        ])
        
        # Apply time-based filtering
        filtered_signals = signals.copy()
        
        # Filter entry signals - no new trades after specified time
        if 'entry_long' in signals:
            entry_long_filtered = df_with_time.select(
                pl.when(pl.col('time_only') > no_trade_time)
                .then(False)
                .otherwise(signals['entry_long'])
                .alias('entry_long')
            ).to_series()
            filtered_signals['entry_long'] = entry_long_filtered
        
        if 'entry_short' in signals:
            entry_short_filtered = df_with_time.select(
                pl.when(pl.col('time_only') > no_trade_time)
                .then(False)
                .otherwise(signals['entry_short'])
                .alias('entry_short')
            ).to_series()
            filtered_signals['entry_short'] = entry_short_filtered
        
        # Add forced exit signals at market close
        if 'exit_long' in signals:
            exit_long_filtered = df_with_time.select(
                pl.when(pl.col('time_only') >= exit_time)
                .then(True)
                .otherwise(signals['exit_long'])
                .alias('exit_long')
            ).to_series()
            filtered_signals['exit_long'] = exit_long_filtered
        
        if 'exit_short' in signals:
            exit_short_filtered = df_with_time.select(
                pl.when(pl.col('time_only') >= exit_time)
                .then(True)
                .otherwise(signals['exit_short'])
                .alias('exit_short')
            ).to_series()
            filtered_signals['exit_short'] = exit_short_filtered
        
        logger.debug(f"Applied intraday rules: no_trade_after={no_trade_after}, exit_all_at={exit_all_at}")
        return filtered_signals
    
    def get_signals_for_strategy(self, df: pl.DataFrame, strategy_name: str, 
                               apply_intraday: bool = True) -> Optional[Dict[str, pl.Series]]:
        """Get signals for a specific strategy by name"""
        strategy = next((s for s in self.strategies if s.get('name') == strategy_name), None)
        if not strategy:
            logger.error(f"Strategy '{strategy_name}' not found")
            return None
        
        signals = self.generate_signals(df, strategy)
        
        if apply_intraday:
            signals = self.apply_intraday_rules(df, signals, strategy)
        
        return signals
    
    def get_strategy_by_name(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """Get strategy configuration by name"""
        return next((s for s in self.strategies if s.get('name') == strategy_name), None)


# Convenience functions for backward compatibility
def load_strategies() -> List[Dict[str, Any]]:
    """Load strategies using SignalAgent"""
    agent = SignalAgent()
    return agent.get_strategies()

def generate_strategy_signals(df: pl.DataFrame, signal_type: str, strategy: dict) -> pl.Series:
    """Generate strategy signals using SignalAgent"""
    agent = SignalAgent()
    signals = agent.generate_signals(df, strategy, [signal_type])
    return signals.get(signal_type, pl.Series("mask", [False] * df.height))
